
#pragma once

#include "CoreMinimal.h"
#include "ThingObj.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "ItemInstance.generated.h"
UENUM(BlueprintType)
enum class EItemRarity:uint8
{
	// 普通
	Normal,
	// 稀有
	Rarely,
	// 史诗
	Epic,
	// 传说
	Legend
};
//表格信息
USTRUCT(BlueprintType)
struct FItemRarityMultiplier
{
    GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EItemRarity Rarity;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float BaseWeight;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float LuckyWeight;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int AffixNumMin;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int AffixNumMax;
	
	static FItemRarityMultiplier FromJson(const TSharedPtr<FJsonObject>& JsonObj)
	{
		FItemRarityMultiplier Info;
		Info.Rarity = UDataFuncLib::AwGetEnumField(JsonObj,"Rarity", EItemRarity::Normal);
		Info.BaseWeight = UDataFuncLib::AwGetNumberField(JsonObj,"BaseWeight",100.0f);
		Info.LuckyWeight = UDataFuncLib::AwGetNumberField(JsonObj,"LuckyWeight",0.0f);
		Info.AffixNumMin = UDataFuncLib::AwGetNumberField(JsonObj,"AffixNumMin",1);
		Info.AffixNumMax = UDataFuncLib::AwGetNumberField(JsonObj,"AffixNumMax",1);
		return Info;
	}
	float WeightByMf(double MagicFound) const;
};
//表格信息
USTRUCT(BlueprintType)
struct FItemAffixModel
{
    GENERATED_BODY()
public:
	// Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int AffixLevel;
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString AffixGroup;
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString BuffId;
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ValueMax;
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ValueMin;
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Weight;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EItemRarity Rarity;
	
	static FItemAffixModel FromJson(const TSharedPtr<FJsonObject>& JsonObj)
	{
		FItemAffixModel Info;
		Info.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
		Info.BuffId = UDataFuncLib::AwGetStringField(JsonObj, "BuffId", "");
		Info.ValueMax = UDataFuncLib::AwGetNumberField(JsonObj, "ValueMax", 233);
		Info.ValueMin = UDataFuncLib::AwGetNumberField(JsonObj, "ValueMin", 233);
		Info.AffixGroup = UDataFuncLib::AwGetStringField(JsonObj, "AffixGroup", "");
		Info.AffixLevel = UDataFuncLib::AwGetNumberField(JsonObj, "AffixLevel", 1);
		Info.Weight = UDataFuncLib::AwGetNumberField(JsonObj, "Weight", 100.0f);
		Info.Rarity = UDataFuncLib::AwGetEnumField(JsonObj,"Rarity", EItemRarity::Normal);
		
		return Info;
	}
};
//实例词条
USTRUCT(BlueprintType)
struct FItemAffix
{
    GENERATED_BODY()

public:
    //从此处词条决定buffid和范围
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString AffixId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Value;
};
USTRUCT(BlueprintType)
struct FItemInstanceModel
{
	GENERATED_BODY()
	// Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EThingType Type;
	// Chinese表Id,武器名称
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Name;
	// ItemIcon表Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Icon;
	// 主手武器模型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MainHand;
	// 副手武器模型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString OffHand;
	// 价格
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Price;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EItemRarity Rarity;
	
	static FItemInstanceModel FromJson(const TSharedPtr<FJsonObject>& JsonObj)
	{
		FItemInstanceModel Info;

		Info.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
		Info.Type = UDataFuncLib::AwGetEnumField(JsonObj,"Type",EThingType::WeaponObj);
		Info.Name = UDataFuncLib::AwGetStringField(JsonObj, "Name", "");
		Info.Icon = UDataFuncLib::AwGetStringField(JsonObj, "Icon", "");
		Info.MainHand = UDataFuncLib::AwGetStringField(JsonObj, "MainHand", "");
		Info.OffHand = UDataFuncLib::AwGetStringField(JsonObj, "OffHand", "");
		Info.Price = UDataFuncLib::AwGetNumberField(JsonObj, "Price", 0);
		Info.Rarity = UDataFuncLib::AwGetEnumField(JsonObj,"Rarity", EItemRarity::Normal);
		
		return Info;
	}
};

USTRUCT(BlueprintType)
struct FItemInstance
{
	GENERATED_BODY()
    //基础武器模板 
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Model;  
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FItemAffix> RandomAffixes;
};

