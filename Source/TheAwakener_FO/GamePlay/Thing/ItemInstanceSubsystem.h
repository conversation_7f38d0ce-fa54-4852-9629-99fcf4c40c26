// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "ItemInstance.h"
#include "ThingObj.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "ItemInstanceSubsystem.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UItemInstanceSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	void ReGetItemInstanceEquipped();

	//WeaponObj or Item, else ALL
	UFUNCTION(BlueprintCallable)
	FItemInstance GetEquippedItemInstance(double MagicFoundRate,EThingType Type);
};
