// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "ItemInstance.h"
#include "ThingObj.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "ItemInstanceSubsystem.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UItemInstanceSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	void ReGetItemInstanceEquipped();

	EItemRarity GenerateRarityByMF(double MagicFoundRate);
	FItemAffix GenerateAffixByMF(TArray<FString> AlreadyGetAffix,double MagicFoundRate);
	//WeaponObj or Item, else ALL
	UFUNCTION(BlueprintCallable)
	FItemInstance GenerateEquippedItemInstance(double MagicFoundRate,EThingType Type);
};
