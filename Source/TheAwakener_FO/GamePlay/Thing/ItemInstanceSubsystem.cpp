// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "ItemInstanceSubsystem.h"

#include "MathUtil.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UItemInstanceSubsystem::ReGetItemInstanceEquipped()
{
	//移除实际效果Buff
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC || !PC->CurCharacter)
		{
			return;
		}
		PC->CurCharacter->RemoveBuffByTag("ItemInstance");
	}
}

EItemRarity UItemInstanceSubsystem::GenerateRarityByMF(double MagicFoundRate)
{
	auto DataManager = UGameplayFuncLib::GetAwDataManager();
	if (!DataManager)
	{
		return EItemRarity::Normal;
	}
	return UCommonFuncLib::WeightedRandomSelectionFromMap<FItemRarityMultiplier>(DataManager->ItemRarityWeights,[&](const FItemRarityMultiplier& Data)->float
	{
		return Data.WeightByMf(MagicFoundRate);
	});
}

FItemAffix UItemInstanceSubsystem::GenerateAffixByMF(TArray<FString> AlreadyGetAffix,double MagicFoundRate)
{
	FItemAffix Res;
	auto DataManager = UGameplayFuncLib::GetAwDataManager();
	if (DataManager)
	{
		FString randomKey = UCommonFuncLib::WeightedRandomSelectionFromMap<FItemAffixModel>(DataManager->ItemInstanceAffixes,[&](const FItemAffixModel& Data)->float
		{return Data.Weight + DataManager->GetRandWeightByMF(Data.Rarity,MagicFoundRate);});
		if (DataManager->ItemInstanceAffixes.Contains(randomKey))
		{
			auto affix = DataManager->ItemInstanceAffixes[randomKey];
			Res.AffixId = randomKey;
			Res.Value = FMath::RandRange(affix.ValueMin,affix.ValueMax);
		}
	}
	return Res;
}

FItemInstance UItemInstanceSubsystem::GenerateEquippedItemInstance(double MagicFoundRate, EThingType Type)
{
	FItemInstance ItemInstance;
	auto DataManager = UGameplayFuncLib::GetAwDataManager();
	if (DataManager)
	{
		FString randomKey = UCommonFuncLib::WeightedRandomSelectionFromMap<FItemInstanceModel>(DataManager->ItemInstanceModels,[](const FItemInstanceModel& Data)->float
		{return 1;});
		ItemInstance.Model = randomKey;
		EItemRarity Rarity = GenerateRarityByMF(MagicFoundRate);
		int AffixNum = FMath::RandRange(DataManager->ItemRarityWeights[Rarity].AffixNumMin,DataManager->ItemRarityWeights[Rarity].AffixNumMax);
		TArray<FString> AlreadyGetAffix;
		for (int i = 0; i < AffixNum; ++i)
		{
			auto NewAffix = GenerateAffixByMF(MagicFoundRate);
			if (!NewAffix.AffixId.IsEmpty())
			{
				ItemInstance.RandomAffixes.Add(NewAffix);
				AlreadyGetAffix.Add(NewAffix.)
			}
		}
	}
	return ItemInstance;
}
