// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "ItemInstanceSubsystem.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UItemInstanceSubsystem::ReGetItemInstanceEquipped()
{
	//移除实际效果Buff
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC || !PC->CurCharacter)
		{
			return;
		}
		PC->CurCharacter->RemoveBuffByTag("ItemInstance");
	}
}

FItemInstance UItemInstanceSubsystem::GetEquippedItemInstance(double MagicFoundRate, EThingType Type)
{
	return FItemInstance();
}
