// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "UObject/Object.h"
#include "MakeClone.generated.h"

/**
 * 在原地制造一个分身（Debug专用）
 */
UCLASS()
class THEAWAKENER_FO_API UMakeClone : public UAnimNotify
{
	GENERATED_BODY()
public:
	//幻象存在多久
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float StayTime = 0.2f;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
