// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "Kismet/GameplayStatics.h"
#include "CommonFuncLib.generated.h"

/**
 * 一些通用函数和算法
 * by MuXian
 */

UCLASS()
class THEAWAKENER_FO_API UCommonFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	template <typename T>
	 static bool ArrayContainsArray(TArray<T>Sources,TArray<T>Targets,bool bFullContain = false)
	{
		if (Targets.IsEmpty())
		{
			return  true;
		}
		bool Res = false;
		if (bFullContain)
		{
			Res = true;
			for (auto Target:Targets)
			{
				if (!Sources.Contains(Target))
				{
					Res = false;
					break; ;
				}
			}
		}
		else
		{
			for (auto Target:Targets)
			{
				if (Sources.Contains(Target))
				{
					Res = true;
					break; ;
				}
			}
		}
		return Res;
	}
	
	UFUNCTION(BlueprintCallable)
	static int AliasMethodRandom(TArray<float>WeightPool,bool ConstructRate = false);

	/**
	 * 通用权重抽取函数，支持任意数据结构的数组
	 * @param DataArray 数据数组
	 * @param WeightGetter Lambda表达式，用于从数据结构中获取权重值
	 * @param ConstructRate 是否需要构造权重比例（归一化）
	 * @return 选中元素的索引，如果数组为空返回-1
	 */
	template<typename T>
	static int WeightedRandomSelection(const TArray<T>& DataArray, TFunction<float(const T&)> WeightGetter, bool ConstructRate = true);

	UFUNCTION(BlueprintCallable)
		static bool SetWindowMode(EWindowMode::Type type);
};


